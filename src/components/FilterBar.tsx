import React from 'react';
import { Filter, MapPin, Users, Clock, Calendar, ChevronDown } from 'lucide-react';

interface FilterBarProps {
  filters: {
    category: string;
    stage: string;
    location: string;
    dateFrom?: string;
    dateTo?: string;
  };
  setFilters: React.Dispatch<React.SetStateAction<{
    category: string;
    stage: string;
    location: string;
    dateFrom?: string;
    dateTo?: string;
  }>>;
}

export function FilterBar({ filters, setFilters }: FilterBarProps) {
  const categories = ['all', 'customer', 'business owner', 'stakeholder'];
  const stages = ['all', 'research', 'validation', 'implementation'];
  const locations = ['all', 'US', 'Europe', 'Asia'];

  const inputClasses = `
    block w-full rounded-lg border-0 px-3 py-2 text-gray-900 shadow-sm 
    ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 
    focus:ring-2 focus:ring-inset focus:ring-blue-600 
    hover:ring-gray-400 transition-all duration-200
    sm:text-sm sm:leading-6
  `;

  const selectClasses = `
    block w-full rounded-lg border-0 px-3 py-2 pr-8 text-gray-900 shadow-sm 
    ring-1 ring-inset ring-gray-300
    focus:ring-2 focus:ring-inset focus:ring-blue-600
    hover:ring-gray-400 transition-all duration-200
    sm:text-sm sm:leading-6
    appearance-none
  `;

  const iconWrapperClasses = `
    absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none
  `;

  const chevronWrapperClasses = `
    absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none
  `;

  return (
    <div className="bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-8 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-8">
            {/* Category Filter */}
            <div className="flex flex-col space-y-1.5 min-w-[180px]">
              <label className="text-xs font-medium text-gray-700">Category</label>
              <div className="relative">
                <div className={iconWrapperClasses}>
                  <Users className="h-4 w-4 text-gray-400" />
                </div>
                <select
                  value={filters.category}
                  onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
                  className={`${selectClasses} pl-10`}
                >
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </option>
                  ))}
                </select>
                <div className={chevronWrapperClasses}>
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                </div>
              </div>
            </div>

            {/* Stage Filter */}
            <div className="flex flex-col space-y-1.5 min-w-[180px]">
              <label className="text-xs font-medium text-gray-700">Research Stage</label>
              <div className="relative">
                <div className={iconWrapperClasses}>
                  <Clock className="h-4 w-4 text-gray-400" />
                </div>
                <select
                  value={filters.stage}
                  onChange={(e) => setFilters(prev => ({ ...prev, stage: e.target.value }))}
                  className={`${selectClasses} pl-10`}
                >
                  {stages.map(stage => (
                    <option key={stage} value={stage}>
                      {stage.charAt(0).toUpperCase() + stage.slice(1)}
                    </option>
                  ))}
                </select>
                <div className={chevronWrapperClasses}>
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                </div>
              </div>
            </div>

            {/* Location Filter */}
            <div className="flex flex-col space-y-1.5 min-w-[180px]">
              <label className="text-xs font-medium text-gray-700">Location</label>
              <div className="relative">
                <div className={iconWrapperClasses}>
                  <MapPin className="h-4 w-4 text-gray-400" />
                </div>
                <select
                  value={filters.location}
                  onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                  className={`${selectClasses} pl-10`}
                >
                  {locations.map(location => (
                    <option key={location} value={location}>
                      {location.charAt(0).toUpperCase() + location.slice(1)}
                    </option>
                  ))}
                </select>
                <div className={chevronWrapperClasses}>
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                </div>
              </div>
            </div>
          </div>

          {/* Date Range */}
          <div className="flex flex-col space-y-1.5">
            <label className="text-xs font-medium text-gray-700">Date Range</label>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <div className={iconWrapperClasses}>
                  <Calendar className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="date"
                  value={filters.dateFrom || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, dateFrom: e.target.value }))}
                  className={`${inputClasses} pl-10`}
                />
              </div>
              <span className="text-gray-400">to</span>
              <input
                type="date"
                value={filters.dateTo || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, dateTo: e.target.value }))}
                className={inputClasses}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}