import React from "react";
import btLogo from "../assets/BT.png";

interface AdminFooterProps {
  className?: string;
}

export const AdminFooter: React.FC<AdminFooterProps> = ({ className = "" }) => {
  return (
    <div className={` ${className}`}>
      <div className='flex items-center space-x-4 py-4'>
        {/* Blood and Treasure Logo */}
        <div className='flex-shrink-0'>
          <img
            src={btLogo}
            alt='Blood and Treasure logo'
            className='w-16 h-16 object-contain rounded-lg'
            onError={(e) => {
              // Fallback to gradient div if image fails to load
              const target = e.target as HTMLImageElement;
              target.style.display = "none";
              const fallback = target.nextElementSibling as HTMLElement;
              if (fallback) fallback.style.display = "flex";
            }}
          />
          <div className='w-16 h-16 bg-gradient-to-br from-red-500 via-yellow-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-xs hidden'>
            B&T
          </div>
        </div>

        {/* Powered by Text */}
        <div className='flex-1'>
          <p className='text-sm text-gray-600 font-medium'>
            powered by{" "}
            <span className='text-primary font-semibold'>
              Blood and Treasure
            </span>
          </p>
        </div>
      </div>
    </div>
  );
};
