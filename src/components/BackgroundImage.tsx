import React, { useEffect } from "react";
import backgroundImage from "../assets/background51.jpg";

const BackgroundImage: React.FC = () => {
  useEffect(() => {
    // Apply background image to body element
    document.body.style.backgroundImage = `url(${backgroundImage})`;
    document.body.style.backgroundSize = "cover";
    document.body.style.backgroundPosition = "center";
    document.body.style.backgroundAttachment = "fixed";
    document.body.style.backgroundRepeat = "no-repeat";

    // Cleanup function to remove background when component unmounts
    return () => {
      document.body.style.backgroundImage = "";
      document.body.style.backgroundSize = "";
      document.body.style.backgroundPosition = "";
      document.body.style.backgroundAttachment = "";
      document.body.style.backgroundRepeat = "";
    };
  }, []);

  return null; // This component doesn't render anything
};

export default BackgroundImage;
