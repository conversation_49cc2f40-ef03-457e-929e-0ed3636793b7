import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Lock, Eye, EyeOff, AlertCircle, CheckCircle } from "lucide-react";
import { adminService } from "../services";
import { API_CONFIG } from "../config/apiConfig";

interface PinAccessProps {
  onSuccess: () => void;
  onCancel?: () => void;
}

export const PinAccess: React.FC<PinAccessProps> = ({
  onSuccess,
  onCancel,
}) => {
  const navigate = useNavigate();
  const [pin, setPin] = useState("");
  const [showPin, setShowPin] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);
  const [pinExists, setPinExists] = useState<boolean | null>(null);

  // Check if PIN exists on component mount
  useEffect(() => {
    checkPinExists();
  }, []);

  const checkPinExists = async () => {
    try {
      const exists = await adminService.pinExists();
      setPinExists(exists);
    } catch (error) {
      console.error("Error checking PIN existence:", error);
      setError("Unable to check PIN status. Please try again.");
    }
  };

  const handlePinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase();
    if (value.length <= 6) {
      setPin(value);
      setError(""); // Clear error when user types
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!pin || pin.length !== 6) {
      setError("Please enter a 6-character PIN");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      const isValid = await adminService.validatePin(pin);

      if (isValid) {
        setSuccess(true);
        // Store authentication in session
        sessionStorage.setItem("adminAuthenticated", "true");
        sessionStorage.setItem("adminPin", pin);

        setTimeout(() => {
          onSuccess();
        }, 1000);
      } else {
        setError("Invalid PIN. Please try again.");
      }
    } catch (error) {
      console.error("Error validating PIN:", error);
      setError(
        "Unable to validate PIN. Please check your connection and try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      navigate("/");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    // Allow only alphanumeric characters
    const char = String.fromCharCode(e.which);
    if (
      !/[A-Za-z0-9]/.test(char) &&
      e.key !== "Backspace" &&
      e.key !== "Delete"
    ) {
      e.preventDefault();
    }
  };

  if (pinExists === null) {
    return (
      <div className='min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100'>
        <div className='bg-white rounded-2xl shadow-xl p-8 w-full max-w-md'>
          <div className='text-center'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto'></div>
            <p className='mt-4 text-gray-600'>Checking PIN status...</p>
          </div>
        </div>
      </div>
    );
  }

  if (pinExists === false) {
    return (
      <div className='min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100'>
        <div className='bg-white rounded-2xl shadow-xl p-8 w-full max-w-md'>
          <div className='text-center'>
            <Lock className='h-12 w-12 text-blue-600 mx-auto mb-4' />
            <h2 className='text-2xl font-bold text-gray-900 mb-2'>
              No PIN Set
            </h2>
            <p className='text-gray-600 mb-6'>
              No admin PIN has been configured. Please contact your
              administrator.
            </p>
            <button
              onClick={handleCancel}
              className='w-full bg-gray-600 text-white py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors'
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100'>
      <div className='bg-white rounded-2xl shadow-xl p-8 w-full max-w-md'>
        <div className='text-center'>
          <Lock className='h-12 w-12 text-blue-600 mx-auto mb-4' />
          <h2 className='text-2xl font-bold text-gray-900 mb-2'>
            Admin Access
          </h2>
          <p className='text-gray-600 mb-6'>
            Enter your 6-character admin PIN to continue
          </p>

          <form onSubmit={handleSubmit} className='space-y-4'>
            <div className='relative'>
              <input
                type={showPin ? "text" : "password"}
                value={pin}
                onChange={handlePinChange}
                onKeyPress={handleKeyPress}
                placeholder='Enter PIN'
                className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-center text-lg font-mono tracking-widest text-black placeholder-gray-500'
                maxLength={6}
                disabled={isLoading || success}
                autoFocus
              />
              <button
                type='button'
                onClick={() => setShowPin(!showPin)}
                className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'
                disabled={isLoading || success}
              >
                {showPin ? (
                  <EyeOff className='h-5 w-5' />
                ) : (
                  <Eye className='h-5 w-5' />
                )}
              </button>
            </div>

            {error && (
              <div className='flex items-center justify-center space-x-2 text-red-600 bg-red-50 p-3 rounded-lg'>
                <AlertCircle className='h-5 w-5' />
                <span className='text-sm font-medium'>{error}</span>
              </div>
            )}

            {success && (
              <div className='flex items-center justify-center space-x-2 text-green-600 bg-green-50 p-3 rounded-lg'>
                <CheckCircle className='h-5 w-5' />
                <span className='text-sm font-medium'>Access granted!</span>
              </div>
            )}

            <div className='flex space-x-3'>
              <button
                type='button'
                onClick={handleCancel}
                className='flex-1 bg-gray-600 text-white py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors'
                disabled={isLoading || success}
              >
                Cancel
              </button>
              <button
                type='submit'
                disabled={isLoading || success || pin.length !== 6}
                className='flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
              >
                {isLoading ? (
                  <div className='flex items-center justify-center'>
                    <div className='animate-spin rounded-full h-5 w-5 border-b-2 border-white'></div>
                    <span className='ml-2'>Validating...</span>
                  </div>
                ) : (
                  "Access"
                )}
              </button>
            </div>
          </form>

          <div className='mt-6 text-xs text-gray-500'>
            <p>PIN must be exactly 6 characters</p>
            <p>Use letters and numbers only</p>
          </div>
        </div>
      </div>
    </div>
  );
};
