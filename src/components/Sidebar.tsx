import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  Video,
  BarChart2,
  Notebook,
  Clock,
  UserCircle,
  ChevronDown,
  Users,
  RefreshCcw,
  PanelLeft,
  PanelRight,
  Settings,
  Bot,
  Workflow,
  FileText,
  BarChart3,
} from "lucide-react";
import classNames from "classnames";
import logo from "../assets/scoot-logo.png";
import { brandApiService } from "../services/brandApiService";
import type { Brand } from "../services/brandApiService";
import { getClientConfig } from "../config/clientConfig";

// SidebarProps defines the props for the Sidebar component
interface SidebarProps {
  activeSection: string;
  onSectionChange: (sectionId: string) => void;
  isCollapsed: boolean;
  setIsCollapsed: (collapsed: boolean) => void;
}

// Sidebar main component
export function Sidebar({
  activeSection,
  onSectionChange,
  isCollapsed,
  setIsCollapsed,
}: SidebarProps) {
  const { brandName } = useParams<{ brandName: string }>();
  const navigate = useNavigate();
  const [isSummaryExpanded, setIsSummaryExpanded] = useState(true);
  const [isAdminExpanded, setIsAdminExpanded] = useState(false);
  const [lastRefreshed, setLastRefreshed] = useState<Date>(new Date());
  const [currentBrand, setCurrentBrand] = useState<Brand | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentClient, setCurrentClient] = useState<{
    name: string;
    logo: string;
  } | null>(null);

  // Load brand based on URL parameter
  useEffect(() => {
    const loadBrand = async () => {
      setLoading(true);
      try {
        if (brandName) {
          const brand = await brandApiService.getBrandByEndpoint(brandName);
          if (brand) {
            setCurrentBrand(brand);
          } else {
            // If brand not found, get default brand
            const defaultBrand = await brandApiService.getDefaultBrand();
            setCurrentBrand(defaultBrand);
          }
        } else {
          // No brand name in URL, get default brand
          const defaultBrand = await brandApiService.getDefaultBrand();
          setCurrentBrand(defaultBrand);
        }
      } catch (error) {
        console.error("Error loading brand in sidebar:", error);
        // Fallback to a basic brand object
        setCurrentBrand({
          id: "fallback",
          name: "Project Roots",
          logo: "",
          endpoint: "fallback",
          clientId: "",
          isDefault: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });
      } finally {
        setLoading(false);
      }
    };

    loadBrand();
  }, [brandName]);

  // Load client information from brand data
  useEffect(() => {
    const loadClientInfo = () => {
      if (currentBrand?.client) {
        // Use client information directly from brand data
        setCurrentClient({
          name: currentBrand.client.name,
          logo: currentBrand.client.logo,
        });
      } else if (loading) {
        // Show loading state
        setCurrentClient(null);
      } else {
        // Fallback to default client info
        setCurrentClient({
          name: "Blood & Treasure",
          logo: "", // Will show initial 'B'
        });
      }
    };

    loadClientInfo();
  }, [currentBrand?.client, loading]);

  // Listen for client config changes to update sidebar footer
  // This allows the "Powered by [Client Name]" text to update immediately when admin saves changes
  useEffect(() => {
    const handleClientConfigChange = () => {
      setClientConfig(getClientConfig());
    };

    // Listen for custom event when client config changes
    window.addEventListener("clientConfigChanged", handleClientConfigChange);

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "clientConfig") {
        setClientConfig(getClientConfig());
      }
    };

    // Also check for changes on storage events (in case of multiple tabs)
    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener(
        "clientConfigChanged",
        handleClientConfigChange
      );
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);

  const menuSections = [
    {
      id: "summary",
      label: "Overview",
      icon: Notebook,
      isExpandable: true,
      items: [
        { id: "progress", icon: BarChart2, label: "Progress" },
        { id: "timeline", icon: Clock, label: "Timeline" },
        { id: "team", icon: UserCircle, label: "Team" },
        { id: "personas", icon: Users, label: "Personas" },
        { id: "media", icon: Video, label: "Media" },
      ],
    },
    {
      id: "admin",
      label: "Admin",
      icon: Settings,
      isExpandable: true,
      items: [
        { id: "ai-chatbot", icon: Bot, label: "AI Chatbot" },
        { id: "team-management", icon: Users, label: "Team Management" },
        { id: "workflow", icon: Workflow, label: "Workflow" },
        { id: "auto-reports", icon: FileText, label: "Auto-Reports" },
        { id: "analytics", icon: BarChart3, label: "Analytics" },
      ],
    },
  ];

  // Handles navigation between sidebar sections
  const handleSectionClick = (sectionId: string) => {
    // Always call onSectionChange to handle scrolling
    onSectionChange(sectionId);

    // Navigate to the appropriate page based on the section
    if (["progress", "timeline", "team", "personas", "media"].includes(sectionId)) {
      // Navigate to overview page with the specific section
      navigate(`/${brandName}/${sectionId}`);
    } else if (["ai-chatbot", "team-management", "workflow", "auto-reports", "analytics"].includes(sectionId)) {
      // Navigate to admin page with the specific section
      navigate(`/${brandName}/${sectionId}`);
    }
  };

  // Handles refresh button click
  const handleRefresh = () => {
    // Add your data refresh logic here
    setLastRefreshed(new Date());
  };

  // Helper function to render client logo or initial
  const renderClientLogo = (clientName: string, clientLogo: string) => {
    if (clientLogo) {
      return (
        <img
          src={clientLogo}
          alt={`${clientName} logo`}
          className='w-10 h-10 rounded-full group-hover:scale-105 transition-transform duration-200'
        />
      );
    } else {
      // Show initial letter in a circular container
      const initial = clientName.charAt(0).toUpperCase();
      return (
        <div className='w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center group-hover:scale-105 transition-transform duration-200'>
          <span className='text-white font-semibold text-lg'>{initial}</span>
        </div>
      );
    }
  };

  return (
    <>
      {/* Toggle button positioned absolutely outside sidebar */}
      <button
        onClick={() => setIsCollapsed(!isCollapsed)}
        className='fixed top-4 p-2 rounded-lg bg-gray-900 text-white hover:bg-gray-700 transition-all duration-300 shadow-lg'
        style={{
          left: isCollapsed ? "72px" : "260px", // Position based on sidebar state
          transform: "translateX(-50%)", // Center the button on the edge
          zIndex: 1050,
        }}
      >
        {isCollapsed ? <PanelRight size={20} /> : <PanelLeft size={20} />}
      </button>

      <aside
        className={classNames(
          "fixed top-0 left-0 h-screen bg-gray-900 text-white shadow-lg transition-all duration-300 flex flex-col sidebar",
          isCollapsed ? "w-16" : "w-64"
        )}
        style={{ zIndex: 1030 }}
      >
        {/* Sidebar Header: logo, title, collapse button */}
        <div className='flex-shrink-0 flex items-center justify-between p-4 border-b border-gray-700 h-20'>
          <div className='flex items-center justify-center'>
            {/* Fixed rectangular container for brand logo - 4:1 ratio like balsam.png */}
            <div
              className={classNames(
                "flex items-center justify-center",
                isCollapsed ? "w-8 h-8" : "w-56 h-14"
              )}
            >
              <img
                src={currentBrand?.logo || logo}
                alt=''
                className={classNames(
                  "object-contain object-center transition-all duration-300", // Removed filters that were inverting colors
                  isCollapsed ? "w-8 h-8" : "w-full h-full"
                )}
                style={{ filter: "brightness(0) invert(1)" }} // This approach preserves logo shape while making it white
              />
            </div>
          </div>
        </div>

        {/* Sidebar Sections: navigation links and expandable sections */}
        <nav className='flex-1 p-2 overflow-y-auto'>
          <ul className='space-y-1'>
            {menuSections.map((section) => (
              <li key={section.id}>
                {section.isExpandable ? (
                  <>
                    {/* Section header with navigation */}
                    <div className='flex items-center'>
                      <button
                        className='flex items-center space-x-2 flex-1 p-2 text-gray-300 hover:bg-gray-700 rounded-lg transition'
                        onClick={() => {
                          if (section.id === "summary") {
                            navigate(`/${brandName}/overview`);
                          } else if (section.id === "admin") {
                            navigate(`/${brandName}/admin`);
                          }
                        }}
                      >
                        {/* Only render icon if defined to avoid JSX type errors */}
                        {section.icon ? <section.icon size={20} /> : <span />}
                        {!isCollapsed && <span>{section.label}</span>}
                      </button>
                      {/* Expand/collapse button */}
                      <button
                        className='p-2 text-gray-300 hover:bg-gray-700 rounded-lg transition'
                        onClick={() => {
                          if (section.id === "summary") {
                            setIsSummaryExpanded(!isSummaryExpanded);
                          } else if (section.id === "admin") {
                            setIsAdminExpanded(!isAdminExpanded);
                          }
                        }}
                      >
                        {!isCollapsed && (
                          <ChevronDown
                            className={
                              (section.id === "summary" && isSummaryExpanded) ||
                              (section.id === "admin" && isAdminExpanded)
                                ? "rotate-180"
                                : ""
                            }
                            size={16}
                          />
                        )}
                      </button>
                    </div>
                    {/* Expandable section items */}
                    {((section.id === "summary" && isSummaryExpanded) ||
                      (section.id === "admin" && isAdminExpanded)) &&
                      section.items && (
                        <ul className='ml-4 mt-1 space-y-1'>
                          {section.items.map((item) => (
                            <SidebarItem
                              key={item.id}
                              {...item}
                              active={activeSection === item.id}
                              collapsed={isCollapsed}
                              onClick={handleSectionClick}
                              data-tour={`${item.id}-nav`}
                            />
                          ))}
                        </ul>
                      )}
                  </>
                ) : section.items ? (
                  // Render non-expandable section with multiple items
                  section.items.map((item) => (
                    <SidebarItem
                      key={item.id}
                      {...item}
                      active={activeSection === item.id}
                      collapsed={isCollapsed}
                      onClick={handleSectionClick}
                      data-tour={`${item.id}-nav`}
                    />
                  ))
                ) : (
                  // Render single section item
                  <SidebarItem
                    id={section.id}
                    icon={section.icon}
                    label={section.label}
                    active={activeSection === section.id}
                    collapsed={isCollapsed}
                    onClick={handleSectionClick}
                    data-tour={`${section.id}-nav`}
                  />
                )}
              </li>
            ))}
          </ul>
        </nav>

        {/* Refresh Section: refresh button and last refreshed time */}
        <div className='flex-shrink-0 border-t border-gray-700'>
          <div className='p-4 space-y-2'>
            <button
              onClick={handleRefresh}
              className='w-full flex items-center justify-center gap-2 p-2 rounded-lg border border-gray-700 bg-gray-800/50 text-gray-400 hover:bg-gray-700 hover:text-white transition-colors'
            >
              <RefreshCcw size={16} />
              {!isCollapsed && <span className='text-sm'>Refresh Data</span>}
            </button>
            {/* Last refreshed time */}
            {!isCollapsed && (
              <p className='text-xs text-gray-500 text-center'>
                Last refreshed: {lastRefreshed.toLocaleTimeString()}
              </p>
            )}
            {isCollapsed && (
              <p className='text-xs text-gray-500 text-center'>
                {"\u200b \u200b"}
              </p>
            )}
          </div>
        </div>

        {/* Footer: Powered by client badge - following AdminFooter styling */}
        <div className='flex-shrink-0 p-4'>
          {isCollapsed ? (
            <button
              onClick={() => navigate("/admin")}
              className={classNames(
                "p-2 flex items-center justify-center h-16 min-h-16 w-16 rounded-lg",
                "text-gray-400 text-xs -ml-4 hover:bg-gray-700 hover:text-white transition-all duration-200 cursor-pointer group"
              )}
              title='Go to Admin Panel'
            >
              {loading ? (
                <div className='w-10 h-10 rounded-full bg-gray-600 flex items-center justify-center'>
                  <div className='animate-spin rounded-full h-6 w-6 border-b-2 border-white'></div>
                </div>
              ) : currentClient ? (
                renderClientLogo(currentClient.name, currentClient.logo)
              ) : (
                <div className='w-10 h-10 rounded-full bg-gray-600 flex items-center justify-center'>
                  <span className='text-white font-semibold text-lg'>?</span>
                </div>
              )}
            </button>
          ) : (
            <button
              onClick={() => navigate("/admin")}
              className={classNames(
                "p-2 flex items-center justify-center gap-2 h-16 min-h-16 w-full rounded-lg",
                "text-gray-400 text-xs hover:bg-gray-700 hover:text-white transition-all duration-200 cursor-pointer group"
              )}
              title='Go to Admin Panel'
            >
              {loading ? (
                <div className='w-10 h-10 rounded-full bg-gray-600 flex items-center justify-center'>
                  <div className='animate-spin rounded-full h-6 w-6 border-b-2 border-white'></div>
                </div>
              ) : currentClient ? (
                renderClientLogo(currentClient.name, currentClient.logo)
              ) : (
                <div className='w-10 h-10 rounded-full bg-gray-600 flex items-center justify-center'>
                  <span className='text-white font-semibold text-lg'>?</span>
                </div>
              )}
              <div
                className={classNames(
                  "transition-all duration-300",
                  isCollapsed
                    ? "opacity-0 translate-x-2 pointer-events-none w-0"
                    : "opacity-100 translate-x-0 w-auto"
                )}
                style={{ minWidth: isCollapsed ? 0 : undefined }}
              >
                <div className='text-center'>
                  <div className='text-sm text-gray-600 font-medium'>
                    powered by
                  </div>
                  <div className='text-primary font-semibold'>
                    {loading ? (
                      <div className='animate-pulse bg-gray-600 h-4 w-24 rounded'></div>
                    ) : (
                      currentClient?.name || "Blood & Treasure"
                    )}
                  </div>
                </div>
              </div>
            </button>
          )}
        </div>
      </aside>
    </>
  );
}

// SidebarItemProps defines the props for a sidebar navigation item
interface SidebarItemProps {
  id: string;
  // Use React.ElementType for Lucide icons to avoid prop type errors
  icon: React.ElementType;
  label: string;
  active: boolean;
  collapsed: boolean;
  onClick: (id: string) => void;
  "data-tour"?: string;
}

// SidebarItem renders a single navigation item in the sidebar
const SidebarItem: React.FC<SidebarItemProps> = ({
  id,
  icon: Icon,
  label,
  active,
  collapsed,
  onClick,
  "data-tour": dataTour,
}) => (
  <li>
    <button
      className={classNames(
        "flex items-center w-full p-2 rounded-lg transition",
        active ? "bg-blue-600 text-white" : "text-gray-300 hover:bg-gray-700"
      )}
      onClick={() => onClick(id)}
      data-tour={dataTour}
    >
      <span className='flex-none w-5 h-5 flex items-center justify-center'>
        <Icon size={20} />
      </span>
      <span
        className={classNames(
          "inline-block transition-all duration-300 ml-3",
          collapsed
            ? "opacity-0 translate-x-2 pointer-events-none w-0"
            : "opacity-100 translate-x-0 w-auto"
        )}
        style={{ minWidth: collapsed ? 0 : undefined }}
      >
        {label}
      </span>
    </button>
  </li>
);
