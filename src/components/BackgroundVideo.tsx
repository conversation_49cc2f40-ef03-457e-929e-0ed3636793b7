import React from "react";

const BackgroundVideo: React.FC = () => {
  return (
    <div
      data-poster-url='https://cdn.prod.website-files.com/5ffcd643561bc26ed27a87a1/5ffcd85058323b1a1485dae4_blue-bg-poster-00001.jpg'
      data-video-urls='https://cdn.prod.website-files.com/5ffcd643561bc26ed27a87a1/5ffcd85058323b1a1485dae4_blue-bg-transcode.mp4,https://cdn.prod.website-files.com/5ffcd643561bc26ed27a87a1/5ffcd85058323b1a1485dae4_blue-bg-transcode.webm'
      data-autoplay='true'
      data-loop='true'
      data-wf-ignore='true'
      className='hero__video w-background-video w-background-video-atom'
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
        zIndex: -1,
        overflow: "hidden",
      }}
    >
      <video
        id='background-video'
        autoPlay
        loop
        muted
        playsInline
        style={{
          backgroundImage:
            "url('https://cdn.prod.website-files.com/5ffcd643561bc26ed27a87a1/5ffcd85058323b1a1485dae4_blue-bg-poster-00001.jpg')",
          width: "100%",
          height: "100%",
          objectFit: "cover",
          position: "absolute",
          top: 0,
          left: 0,
        }}
        data-wf-ignore='true'
        data-object-fit='cover'
      >
        <source
          src='https://cdn.prod.website-files.com/5ffcd643561bc26ed27a87a1/5ffcd85058323b1a1485dae4_blue-bg-transcode.mp4'
          data-wf-ignore='true'
        />
        <source
          src='https://cdn.prod.website-files.com/5ffcd643561bc26ed27a87a1/5ffcd85058323b1a1485dae4_blue-bg-transcode.webm'
          data-wf-ignore='true'
        />
      </video>
    </div>
  );
};

export default BackgroundVideo;
