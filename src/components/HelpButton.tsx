import { HelpCircle } from "lucide-react";
import { resetTourCompletion } from "../services/tourService";

export function HelpButton() {
  const handleClick = () => {
    // Reset tour completion and reload to restart tours
    resetTourCompletion();
    window.location.reload();
  };

  return (
    <button
      onClick={handleClick}
      className='fixed bottom-8 right-8 p-3 bg-white rounded-full shadow-lg hover:shadow-xl transition-shadow duration-200 text-gray-600 hover:text-gray-800 z-50 help-button'
      data-tour='help-button'
    >
      <HelpCircle className='h-6 w-6' />
    </button>
  );
}
