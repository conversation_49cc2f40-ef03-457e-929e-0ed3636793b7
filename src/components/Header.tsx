import React, { useState, useEffect, useRef, useId } from "react";
import { Share2, <PERSON>rkle, X } from "lucide-react";
import { useParams } from "react-router-dom";
import { brandApiService } from "../services/brandApiService";
import type { Brand } from "../services/brandApiService";
import { GlassyButton } from "./GlassyButton";
import { useOverlayState } from "../utils/overlayState";
import { useScrollManager } from "../utils/scrollManager";

interface HeaderProps {
  isCollapsed: boolean;
}

export function Header({ isCollapsed }: HeaderProps) {
  const { brandName } = useParams<{ brandName: string }>();

  const [currentBrand, setCurrentBrand] = useState<Brand | null>(null);
  const [loading, setLoading] = useState(true);
  const [showAiTooltip, setShowAiTooltip] = useState(false);
  const aiButtonRef = useRef<HTMLButtonElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const modalId = useId(); // Generate unique ID for this modal instance
  
  // Register AI tooltip with the overlay system
  useOverlayState(showAiTooltip);
  
  // Use the scroll manager to preserve scroll position (Header AI tooltip is detected as regular)
  useScrollManager(modalId, showAiTooltip, 'Header');

  // Load brand based on URL parameter
  useEffect(() => {
    const loadBrand = async () => {
      setLoading(true);
      try {
        if (brandName) {
          const brand = await brandApiService.getBrandByEndpoint(brandName);
          if (brand) {
            setCurrentBrand(brand);
          } else {
            // If brand not found, get default brand
            const defaultBrand = await brandApiService.getDefaultBrand();
            setCurrentBrand(defaultBrand);
          }
        } else {
          // No brand name in URL, get default brand
          const defaultBrand = await brandApiService.getDefaultBrand();
          setCurrentBrand(defaultBrand);
        }
      } catch (error) {
        console.error("Error loading brand in header:", error);
        // Fallback to a basic brand object
        setCurrentBrand({
          id: "fallback",
          name: "Project Roots",
          logo: "",
          endpoint: "fallback",
          clientId: "",
          isDefault: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });
      } finally {
        setLoading(false);
      }
    };

    loadBrand();
  }, [brandName]);

  // Calculate tooltip position
  const getTooltipPosition = () => {
    if (!aiButtonRef.current) return { top: 0, left: 0 };

    const rect = aiButtonRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current?.getBoundingClientRect();
    const headerHeight = 64; // Header height in pixels
    const gap = 12; // Slightly larger gap between header and tooltip

    // Position tooltip right below the header, aligned to the right edge of AI button
    const top = headerHeight + gap;
    let left = rect.right - (tooltipRect?.width || 320); // Align right edge of tooltip to right edge of AI button

    // Ensure tooltip stays within viewport
    const viewportWidth = window.innerWidth;
    const minLeft = 20;
    const maxLeft = viewportWidth - (tooltipRect?.width || 320) - 20;

    if (left < minLeft) left = minLeft;
    if (left > maxLeft) left = maxLeft;

    return { top, left };
  };

  return (
    <>
      <div className='header-spacer'></div>
      <header
        className={`header ${isCollapsed ? "left-16" : "left-64"}`}
        style={{ zIndex: 1040 }}
        data-tour='header'
      >
        <div className='header-content'>
          <div className='header-brand'>
            <div className='flex items-start'>
              {/* <span className='header-brand-label'>Name:</span> */}
              <div>
                {/* <h1 className='header-brand-title'>{currentBrand.name}</h1> */}
                <h1 className='header-brand-title'>Project Roots</h1>
                <p className='header-brand-subtitle'>Persona Immersion</p>
              </div>
            </div>
          </div>

          <div className='header-actions'>
            <div className='header-user-status'>
              <div className='header-user-indicator'></div>
              <span className='header-user-email'>
                Logged in with: <EMAIL>
              </span>
            </div>

            <GlassyButton
              icon={Share2}
              onClick={() => {
                // Disabled popup modal - just copy link to clipboard
                navigator.clipboard.writeText(window.location.href);
                alert("Link copied to clipboard!");
              }}
              ariaLabel='Share'
            />
            <button
              ref={aiButtonRef}
              onClick={() => {
                setShowAiTooltip(!showAiTooltip);
              }}
              className='header-ai-button group'
              aria-label='AI Bot'
            >
              <Sparkle className='h-5 w-5 transition-transform duration-1000 ease-in-out group-hover:animate-spin' />
            </button>
          </div>
        </div>
      </header>

      {/* AI Tooltip */}
      {showAiTooltip && (
        <>
          {/* Overlay */}
          <div
            className='fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm z-40'
            onClick={() => setShowAiTooltip(false)}
          />

          {/* Tooltip */}
          <div
            ref={tooltipRef}
            className='fixed z-50 w-80'
            style={{
              top: `${getTooltipPosition().top}px`,
              left: `${getTooltipPosition().left}px`,
            }}
          >
            <div className='neumorphic-container shadow-2xl p-6'>
              {/* Header */}
              <div className='flex items-center justify-between mb-4'>
                <div></div>
                <button
                  onClick={() => setShowAiTooltip(false)}
                  className='p-1 hover:bg-gray-100 rounded-full transition-colors'
                  title='Close tooltip'
                >
                  <X className='w-4 h-4 text-gray-500' />
                </button>
              </div>

              {/* Content */}
              <div className='mb-4'>
                <h3 className='font-bold text-gray-300 mb-2 text-lg'>
                  AI Features
                </h3>
                <div className='text-gray-100 leading-relaxed font-medium text-sm'>
                  <p>
                    Want AI chat or instant reports? Those features can be built
                    right in.
                  </p>
                </div>
              </div>

              {/* Actions */}
              <div className='flex items-center justify-between'>
                <div className='flex-1'></div>
                <div className='flex items-center space-x-2'>
                  <button
                    onClick={() => setShowAiTooltip(false)}
                    className='px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors shadow-lg hover:shadow-xl'
                  >
                    Got it
                  </button>
                </div>
                <div className='flex-1'></div>
              </div>
            </div>

            {/* Arrow */}
            <div className='absolute w-3 h-3 bg-white transform rotate-45 shadow-lg top-0 -mt-1.5 right-4' />
          </div>
        </>
      )}
    </>
  );
}
