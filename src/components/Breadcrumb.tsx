import React from 'react';
import { useLocation, Link } from 'react-router-dom';
import { ChevronRight } from 'lucide-react';

const PERSONAS = {
  '1': '<PERSON>',
  '2': '<PERSON>',
  '3': '<PERSON>',
  '4': '<PERSON>',
  '5': '<PERSON>',
  '6': 'David Park'
};

export function Breadcrumb() {
  const location = useLocation();
  const pathnames = location.pathname.split('/').filter(x => x);

  const breadcrumbMap: { [key: string]: string } = {
    home: 'Home',
    personas: 'Personas',
    progress: 'Progress',
    timeline: 'Timeline',
    team: 'Team',
    videos: 'Videos',
    podcasts: 'Podcasts'
  };

  // Only show breadcrumb if we're deeper than /home
  if (pathnames.length <= 1) return null;

  // Get persona name if we're on a persona detail page
  const isPersonaDetail = pathnames.length === 3 && pathnames[1] === 'personas';
  const personaId = isPersonaDetail ? pathnames[2] : null;
  const personaName = personaId ? PERSONAS[personaId as keyof typeof PERSONAS] : null;

  return (
    <nav className="px-8 py-4">
      <ol className="flex items-center space-x-2">
        {pathnames.map((name, index) => {
          const routeTo = `/${pathnames.slice(0, index + 1).join('/')}`;
          const isLast = index === pathnames.length - 1;
          const isId = !breadcrumbMap[name];

          // Skip the ID segment in the breadcrumb
          if (isId && !isPersonaDetail) return null;

          return (
            <li key={name} className="flex items-center">
              {index > 0 && (
                <ChevronRight className="h-4 w-4 text-gray-400 mx-2" />
              )}
              {!isLast ? (
                <Link
                  to={routeTo}
                  className="text-sm text-gray-500 hover:text-gray-700 transition-colors duration-200"
                >
                  {breadcrumbMap[name]}
                </Link>
              ) : (
                <span className="text-sm font-medium text-gray-900">
                  {isPersonaDetail ? personaName : breadcrumbMap[name]}
                </span>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
}