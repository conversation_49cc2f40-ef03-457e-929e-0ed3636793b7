/* ==========================================================================
   COMPONENT-SPECIFIC STYLES
   ========================================================================== */

/* ==========================================================================
   OVERLAY STATE MANAGEMENT
   ========================================================================== */

/* Disable header and sidebar buttons when overlays are active */
.overlay-active .header button,
.overlay-active .header .header-ai-button,
.overlay-active .header .glassy-button,
.overlay-active .sidebar button,
.overlay-active .sidebar .nav-item,
.overlay-active .sidebar .sidebar-toggle {
  pointer-events: none !important;
  opacity: 0.5 !important;
  cursor: not-allowed !important;
}

/* ==========================================================================
   BUTTON COMPONENTS
   ========================================================================== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-tight);
  transition: all var(--transition-base);
  border: none;
  cursor: pointer;
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
}

.btn:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Button Variants */
.btn-primary {
  background-color: var(--color-primary-600);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-primary-700);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-primary:active {
  background-color: var(--color-primary-800);
  transform: translateY(0);
}

.btn-secondary {
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
  border: 1px solid var(--color-gray-300);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-gray-200);
  border-color: var(--color-gray-400);
}

.btn-ghost {
  background-color: transparent;
  color: var(--color-gray-600);
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
}

.btn-icon {
  padding: var(--spacing-2);
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);
  min-width: 40px;
  min-height: 40px;
}

.btn-icon:hover:not(:disabled) {
  background-color: var(--color-gray-100);
  transform: scale(1.05);
}

/* Button Sizes */
.btn-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-xs);
  border-radius: var(--radius-md);
}

.btn-lg {
  padding: var(--spacing-4) var(--spacing-6);
  font-size: var(--font-size-base);
  border-radius: var(--radius-xl);
}

/* ==========================================================================
   HEADER COMPONENTS
   ========================================================================== */

.header {
  position: fixed;
  top: 0;
  right: 0;
  height: var(--header-height);
  /* Completely transparent background to show body background through */
  background-color: transparent;
  /* Subtle border for separation */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: var(--z-fixed);
  transition: left var(--transition-base);
  backdrop-filter: blur(4px) brightness(0.6);
}

.header-content {
  height: var(--header-height);
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-8);
  max-width: var(--container-max-width);
  margin: 0 auto;
}

.header-brand {
  flex: 1;
}

.header-brand-label {
  color: var(--color-gray-500);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-medium);
  margin-right: var(--spacing-2);
}

.header-brand-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary-50);
  margin: 0;
}

.header-brand-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-gray-50);
  margin: 0;
}

.header-navigation {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-nav-list {
  display: flex;
  gap: var(--spacing-1);
}

.header-nav-item {
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-xl);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-base);
  border: none;
  background: none;
  cursor: pointer;
}

.header-nav-item-active {
  /* Glass effect for active navigation */
  background: rgba(59, 130, 246, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.913);
  color: var(--color-primary-50);
}

.header-nav-item-inactive {
  color: var(--color-gray-50);
}

.header-nav-item-inactive:hover {
  /* Glass effect on hover */
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--color-gray-100);
}

.header-actions {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-4);
}

.header-user-status {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2);
  /* Glass effect for user status */
  background: rgba(53, 48, 48, 0);
  backdrop-filter: blur(10px), brightness(140%);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
}

.header-user-indicator {
  width: 6px;
  height: 6px;
  background-color: var(--color-success-500);
  border-radius: var(--radius-full);
  backdrop-filter: brightness(130%);
}

.header-user-email {
  font-size: var(--font-size-xs);
  color: var(--color-gray-100);
}

.header-action-button {
  padding: var(--spacing-2);
  border-radius: var(--radius-lg);
  color: var(--color-gray-500);
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all var(--transition-base);
}

.header-action-button:hover {
  /* Glass effect on hover */
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--color-gray-700);
}

.header-ai-button {
  padding: var(--spacing-2);
  border-radius: var(--radius-lg);
  background: linear-gradient(
    to right,
    var(--color-primary-500),
    var(--color-secondary-500)
  );
  color: white;
  border: none;
  cursor: pointer;
  transition: all var(--transition-slower);
  position: relative;
  overflow: hidden;
}

.header-ai-button:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.header-ai-button:active {
  transform: translateY(0);
}

.header-spacer {
  height: var(--header-height);
}

/* ==========================================================================
   GLASSY BUTTON COMPONENT
   ========================================================================== */

.glassy-button {
  padding: var(--spacing-2);
  border-radius: var(--radius-lg);
  color: var(--color-gray-50);
  background-color: var(--color-neumorphic-bg);
  cursor: pointer;
  transition: all var(--transition-base);
  /* Neumorphic container effect */
  backdrop-filter: blur(40px) brightness(110%) saturate(120%);
  box-shadow: var(--shadow-neumorphic-container);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.glassy-button:hover {
  /* Neumorphic container hover effect */
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(18px) brightness(110%) saturate(120%);
}

.glassy-button:active {
  transform: translateY(0);
}

.glassy-button:focus {
  outline: none;
  box-shadow: var(--shadow-neumorphic-container),
    0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ==========================================================================
   CARD COMPONENTS
   ========================================================================== */

.card {
  background-color: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-100);
  overflow: hidden;
  transition: all var(--transition-base);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--color-gray-100);
  background-color: var(--color-gray-50);
}

.card-body {
  padding: var(--spacing-6);
}

.card-footer {
  padding: var(--spacing-6);
  border-top: 1px solid var(--color-gray-100);
  background-color: var(--color-gray-50);
}

/* Card Variants */
.card-interactive {
  cursor: pointer;
  transition: all var(--transition-base);
}

.card-interactive:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.card-glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* ==========================================================================
   FORM COMPONENTS
   ========================================================================== */

.form-input {
  display: block;
  width: 100%;
  padding: var(--spacing-3);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  color: var(--color-gray-900);
  background-color: white;
  transition: all var(--transition-base);
}

.form-input::placeholder {
  color: var(--color-gray-400);
}

.form-input:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:hover:not(:focus) {
  border-color: var(--color-gray-400);
}

.form-input:disabled {
  background-color: var(--color-gray-50);
  color: var(--color-gray-500);
  cursor: not-allowed;
}

.form-select {
  display: block;
  width: 100%;
  padding: var(--spacing-3);
  padding-right: var(--spacing-8);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  color: var(--color-gray-900);
  background-color: white;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-2) center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  appearance: none;
  transition: all var(--transition-base);
}

.form-select:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-select:hover:not(:focus) {
  border-color: var(--color-gray-400);
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-2);
}

.form-error {
  color: var(--color-error-600);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-1);
}

/* ==========================================================================
   MODAL COMPONENTS
   ========================================================================== */

.modal-backdrop {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  z-index: var(--z-modal-backdrop);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
}

.modal-container {
  position: relative;
  width: 100%;
  max-width: 32rem;
  max-height: calc(100vh - var(--spacing-8));
  overflow: hidden;
  border-radius: var(--radius-2xl);
  background-color: white;
  box-shadow: var(--shadow-2xl);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--color-gray-100);
  background-color: var(--color-gray-50);
}

.modal-body {
  padding: var(--spacing-6);
  flex: 1;
  overflow-y: auto;
}

.modal-footer {
  padding: var(--spacing-6);
  border-top: 1px solid var(--color-gray-100);
  background-color: var(--color-gray-50);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-3);
}

.modal-close {
  padding: var(--spacing-2);
  border-radius: var(--radius-lg);
  color: var(--color-gray-500);
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all var(--transition-base);
}

.modal-close:hover {
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
}

/* ==========================================================================
   NAVIGATION COMPONENTS
   ========================================================================== */

.nav-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: var(--spacing-2);
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);
  text-decoration: none;
  color: inherit;
  border: none;
  background: none;
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.nav-item-active {
  background-color: var(--color-primary-600);
  color: white;
  box-shadow: var(--shadow-sm);
}

.nav-item-inactive {
  color: var(--color-gray-300);
}

.nav-item-inactive:hover {
  background-color: var(--color-gray-700);
  color: white;
}

.nav-item-icon {
  width: 20px;
  height: 20px;
  margin-right: var(--spacing-2);
  flex-shrink: 0;
}

/* ==========================================================================
   BADGE COMPONENTS
   ========================================================================== */

.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-2-5);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  white-space: nowrap;
}

.badge-primary {
  background-color: var(--color-primary-100);
  color: var(--color-primary-800);
}

.badge-secondary {
  background-color: var(--color-gray-100);
  color: var(--color-gray-800);
}

.badge-success {
  background-color: var(--color-success-50);
  color: var(--color-success-600);
}

.badge-warning {
  background-color: var(--color-warning-50);
  color: var(--color-warning-600);
}

.badge-error {
  background-color: var(--color-error-50);
  color: var(--color-error-600);
}

/* ==========================================================================
   PROGRESS COMPONENTS
   ========================================================================== */

.progress-bar {
  height: var(--spacing-2);
  flex: 1;
  border-radius: var(--radius-full);
  background-color: var(--color-gray-200);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(
    to right,
    var(--color-primary-500),
    var(--color-success-500)
  );
  border-radius: var(--radius-full);
  transition: width var(--transition-slower);
}

.progress-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-1);
}

/* ==========================================================================
   TIMELINE COMPONENTS
   ========================================================================== */

.timeline-container {
  position: relative;
  padding: 0 var(--spacing-4);
}

.timeline-line {
  position: absolute;
  left: var(--spacing-8);
  top: 0;
  height: 100%;
  width: 1px;
  background-color: var(--color-gray-200);
}

.timeline-item {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-8);
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: var(--spacing-8);
  transform: translateX(-50%);
  width: var(--spacing-6);
  height: var(--spacing-6);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 10;
}

.timeline-dot-completed {
  background-color: var(--color-success-500);
}

.timeline-dot-current {
  background-color: var(--color-primary-500);
  box-shadow: 0 0 0 4px var(--color-primary-100);
}

.timeline-dot-pending {
  border: 2px solid var(--color-gray-300);
  background-color: white;
}

.timeline-content {
  margin-left: var(--spacing-16);
  transform: translateY(0);
  transition: transform var(--transition-base);
}

.timeline-item:hover .timeline-content {
  transform: translateY(-4px);
}

.timeline-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-1);
}

.timeline-date {
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
}

/* ==========================================================================
   PERSONA COMPONENTS
   ========================================================================== */

.persona-card {
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  transition: all var(--transition-base);
  height: 500px;
  display: flex;
  flex-direction: column;
  background-color: white;
  cursor: pointer;
}

.persona-card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px) scale(1.02);
}

.persona-card:focus {
  outline: none;
  box-shadow: var(--shadow-xl), 0 0 0 2px var(--color-primary-500),
    0 0 0 4px rgba(59, 130, 246, 0.1);
}

.persona-card-header {
  padding: var(--spacing-4);
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.persona-card-avatar {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  object-fit: cover;
}

.persona-card-body {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 0 var(--spacing-4);
  overflow-y: auto;
}

.persona-card-footer {
  padding: var(--spacing-2) var(--spacing-4) var(--spacing-4);
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.persona-category-badge {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
}

/* ==========================================================================
   CAROUSEL COMPONENTS
   ========================================================================== */

.carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  padding: var(--spacing-3);
  border-radius: var(--radius-full);
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  box-shadow: var(--shadow-lg);
  border: none;
  cursor: pointer;
  transition: all var(--transition-base);
  z-index: 20;
}

.carousel-arrow:hover {
  background-color: white;
  transform: translateY(-50%) scale(1.1);
}

.carousel-arrow.left-8 {
  left: var(--spacing-8);
}

.carousel-arrow.right-8 {
  right: var(--spacing-8);
}

.carousel-dot {
  width: var(--spacing-4);
  height: var(--spacing-4);
  border-radius: var(--radius-full);
  border: 2px solid;
  background: none;
  cursor: pointer;
  transition: all var(--transition-base);
  outline: none;
}

.carousel-dot-active {
  background-color: var(--color-primary-600);
  border-color: var(--color-primary-600);
  box-shadow: var(--shadow-lg);
  transform: scale(1.1);
}

.carousel-dot-inactive {
  background-color: rgba(255, 255, 255, 0.5);
  border-color: var(--color-gray-300);
}

.carousel-dot-inactive:hover {
  background-color: rgba(255, 255, 255, 0.8);
}

/* ==========================================================================
   LAYOUT COMPONENTS
   ========================================================================== */

.section-container {
  margin-bottom: var(--spacing-16);
}

.section-container:last-child {
  margin-bottom: 0;
}

.grid-responsive {
  display: grid;
  gap: var(--spacing-6);
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

@media (min-width: 768px) {
  .grid-responsive {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }
}

@media (min-width: 1024px) {
  .grid-responsive {
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  }
}

.divider {
  height: 1px;
  background-color: var(--color-gray-200);
  margin: var(--spacing-6) 0;
}

.spacer {
  height: var(--spacing-6);
}

/* ==========================================================================
   UTILITY CLASSES
   ========================================================================== */

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-balance {
  text-wrap: balance;
}

/* ==========================================================================
   ANIMATION CLASSES
   ========================================================================== */

.fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.slide-up {
  animation: slideUp 0.3s ease-out forwards;
}

.scale-in {
  animation: scaleIn 0.2s ease-out forwards;
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
