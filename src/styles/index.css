@tailwind base;
@tailwind components;
@tailwind utilities;

/* ==========================================================================
   SCOOT INSIGHTS DESIGN SYSTEM
   ========================================================================== */

/* ==========================================================================
   DESIGN TOKENS (CSS CUSTOM PROPERTIES)
   ========================================================================== */

:root {
  /* ==========================================================================
     COLORS
     ========================================================================== */

  /* Primary Colors */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  /* Secondary Colors */
  --color-secondary-50: #faf5ff;
  --color-secondary-100: #f3e8ff;
  --color-secondary-200: #e9d5ff;
  --color-secondary-300: #d8b4fe;
  --color-secondary-400: #c084fc;
  --color-secondary-500: #a855f7;
  --color-secondary-600: #9333ea;
  --color-secondary-700: #7c3aed;
  --color-secondary-800: #6b21a8;
  --color-secondary-900: #581c87;

  /* Gray Scale */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* Semantic Colors */
  --color-success-50: #f0fdf4;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-warning-50: #fffbeb;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-error-50: #fef2f2;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;

  /* Neumorphic Colors - Enhanced for nature background */
  --color-neumorphic-bg: rgba(
    255,
    255,
    255,
    0.1
  ); /* More transparent for glassy effect */
  --color-neumorphic-shadow-light: rgba(
    255,
    255,
    255,
    0.15
  ); /* Subtle light shadow */
  --color-neumorphic-shadow-dark: rgba(
    0,
    0,
    0,
    0.08
  ); /* Very subtle dark shadow */

  /* Header Colors */
  --color-header-bg: rgba(255, 255, 255, 0.9);

  /* Text Colors - Optimized for glassy neumorphic effect against nature background */
  --color-text-primary: #ffffff; /* Pure white for headings and important text */
  --color-text-secondary: rgba(
    255,
    255,
    255,
    0.9
  ); /* Semi-transparent white for body text */
  --color-text-muted: rgba(
    255,
    255,
    255,
    0.7
  ); /* More transparent white for subtle text */
  --color-text-inverse: #1a1a1a; /* Dark text for light backgrounds */
  --color-text-accent: #fbbf24; /* Warm golden accent for links and highlights */

  /* ==========================================================================
     TYPOGRAPHY
     ========================================================================== */

  --font-family-sans: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, sans-serif;
  --font-family-mono: "JetBrains Mono", "Fira Code", Consolas, monospace;

  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* ==========================================================================
     SPACING
     ========================================================================== */

  --spacing-0: 0;
  --spacing-px: 1px;
  --spacing-0-5: 0.125rem;
  --spacing-1: 0.25rem;
  --spacing-1-5: 0.375rem;
  --spacing-2: 0.5rem;
  --spacing-2-5: 0.625rem;
  --spacing-3: 0.75rem;
  --spacing-3-5: 0.875rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-7: 1.75rem;
  --spacing-8: 2rem;
  --spacing-9: 2.25rem;
  --spacing-10: 2.5rem;
  --spacing-11: 2.75rem;
  --spacing-12: 3rem;
  --spacing-14: 3.5rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  --spacing-28: 7rem;
  --spacing-32: 8rem;
  --spacing-36: 9rem;
  --spacing-40: 10rem;
  --spacing-44: 11rem;
  --spacing-48: 12rem;
  --spacing-52: 13rem;
  --spacing-56: 14rem;
  --spacing-60: 15rem;
  --spacing-64: 16rem;
  --spacing-72: 18rem;
  --spacing-80: 20rem;
  --spacing-96: 24rem;

  /* ==========================================================================
     BORDER RADIUS
     ========================================================================== */

  --radius-none: 0;
  --radius-sm: 0.125rem;
  --radius-base: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 2.1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* ==========================================================================
     SHADOWS
     ========================================================================== */

  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);

  /* Neumorphic Shadows */
  --shadow-neumorphic-elevated: 6px 6px 12px var(--color-neumorphic-shadow-dark),
    -6px -6px 12px var(--color-neumorphic-shadow-light);
  --shadow-neumorphic-inset: inset 6px 6px 12px
      var(--color-neumorphic-shadow-dark),
    inset -6px -6px 12px var(--color-neumorphic-shadow-light);
  --shadow-neumorphic-container: inset 10px 10px 50px
      var(--color-neumorphic-shadow-light),
    inset -15px -15px 50px var(--color-neumorphic-shadow-dark);

  /* ==========================================================================
     LAYOUT
     ========================================================================== */

  --sidebar-width-expanded: 256px;
  --sidebar-width-collapsed: 64px;
  --header-height: 64px;
  --container-max-width: 1280px;
  --container-padding: var(--spacing-8);

  /* ==========================================================================
     Z-INDEX
     ========================================================================== */

  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1100;
  --z-modal: 1110;
  --z-popover: 1060;
  --z-tooltip: 1070;

  /* ==========================================================================
     TRANSITIONS
     ========================================================================== */

  --transition-fast: 150ms ease-in-out;
  --transition-base: 200ms ease-in-out;
  --transition-slow: 300ms ease-in-out;
  --transition-slower: 500ms ease-in-out;
}

/* ==========================================================================
   BASE STYLES
   ========================================================================== */

* {
  box-sizing: border-box;
}

html {
  font-family: var(--font-family-sans);
  line-height: var(--line-height-normal);
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ==========================================================================
   NEUMORPHIC STYLES
   ========================================================================== */

/* Neumorphic Container */
.neumorphic-container {
  background-color: var(--color-neumorphic-bg);
  position: absolute;
  border-radius: var(--radius-2xl);
  /* background: var(--color-neumorphic-bg); */
  backdrop-filter: blur(40px) brightness(110%) saturate(120%);
  box-shadow: var(--shadow-neumorphic-container);
  padding: var(--spacing-14);
  position: relative;
  transition: all var(--transition-base);
}

/* Persona Overview Carousel Card - Dynamic Color-Coded Gradients */
.persona-overview-card {
  backdrop-filter: blur(20px) brightness(1.3) saturate(1.5) !important;
  -webkit-backdrop-filter: blur(20px) brightness(1.3) saturate(1.5) !important;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25) !important;
}

/* Ambitious - Orange/Amber Gradient */
.persona-overview-card.ambitious {
  background: linear-gradient(
    135deg,
    rgba(251, 191, 36, 0.45) 0%,
    rgba(245, 158, 11, 0.65) 25%,
    rgba(217, 119, 6, 0.85) 50%,
    rgba(194, 65, 12, 0.65) 75%,
    rgba(180, 83, 9, 0.45) 100%
  ) !important;
  border: 1px solid rgba(251, 191, 36, 0.6) !important;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(251, 191, 36, 0.3) !important;
}

/* Architect - Blue/Indigo Gradient */
.persona-overview-card.architect {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.45) 0%,
    rgba(37, 99, 235, 0.65) 25%,
    rgba(29, 78, 216, 0.85) 50%,
    rgba(67, 56, 202, 0.65) 75%,
    rgba(99, 102, 241, 0.45) 100%
  ) !important;
  border: 1px solid rgba(59, 130, 246, 0.6) !important;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(59, 130, 246, 0.3) !important;
}

/* Contented - Green/Emerald Gradient */
.persona-overview-card.contented {
  background: linear-gradient(
    135deg,
    rgba(34, 197, 94, 0.45) 0%,
    rgba(22, 163, 74, 0.65) 25%,
    rgba(21, 128, 61, 0.85) 50%,
    rgba(5, 150, 105, 0.65) 75%,
    rgba(6, 182, 212, 0.45) 100%
  ) !important;
  border: 1px solid rgba(34, 197, 94, 0.6) !important;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(34, 197, 94, 0.3) !important;
}

/* Sophisticate - Purple/Violet Gradient */
.persona-overview-card.sophisticate {
  background: linear-gradient(
    135deg,
    rgba(168, 85, 247, 0.45) 0%,
    rgba(147, 51, 234, 0.65) 25%,
    rgba(126, 34, 206, 0.85) 50%,
    rgba(139, 92, 246, 0.65) 75%,
    rgba(124, 58, 237, 0.45) 100%
  ) !important;
  border: 1px solid rgba(168, 85, 247, 0.6) !important;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(168, 85, 247, 0.3) !important;
}

/* Overcomer - Teal/Cyan Gradient */
.persona-overview-card.overcomer {
  background: linear-gradient(
    135deg,
    rgba(20, 184, 166, 0.45) 0%,
    rgba(13, 148, 136, 0.65) 25%,
    rgba(15, 118, 110, 0.85) 50%,
    rgba(8, 145, 178, 0.65) 75%,
    rgba(14, 165, 233, 0.45) 100%
  ) !important;
  border: 1px solid rgba(20, 184, 166, 0.6) !important;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(20, 184, 166, 0.3) !important;
}

/* Traditionalist - Brown/Amber Gradient */
.persona-overview-card.traditionalist {
  background: linear-gradient(
    135deg,
    rgba(245, 158, 11, 0.45) 0%,
    rgba(217, 119, 6, 0.65) 25%,
    rgba(180, 83, 9, 0.85) 50%,
    rgba(146, 64, 14, 0.65) 75%,
    rgba(120, 53, 15, 0.45) 100%
  ) !important;
  border: 1px solid rgba(245, 158, 11, 0.6) !important;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(245, 158, 11, 0.3) !important;
}

/* Minimalist - Gray/Slate Gradient */
.persona-overview-card.minimalist {
  background: linear-gradient(
    135deg,
    rgba(148, 163, 184, 0.45) 0%,
    rgba(100, 116, 139, 0.65) 25%,
    rgba(71, 85, 105, 0.85) 50%,
    rgba(51, 65, 85, 0.65) 75%,
    rgba(30, 41, 59, 0.45) 100%
  ) !important;
  border: 1px solid rgba(148, 163, 184, 0.6) !important;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(148, 163, 184, 0.3) !important;
}

/* Over-Achiever - Red/Crimson Gradient */
.persona-overview-card.over-achiever {
  background: linear-gradient(
    135deg,
    rgba(239, 68, 68, 0.45) 0%,
    rgba(220, 38, 38, 0.65) 25%,
    rgba(185, 28, 28, 0.85) 50%,
    rgba(153, 27, 27, 0.65) 75%,
    rgba(127, 29, 29, 0.45) 100%
  ) !important;
  border: 1px solid rgba(239, 68, 68, 0.6) !important;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(239, 68, 68, 0.3) !important;
}

/* Curious - Yellow/Amber Gradient */
.persona-overview-card.curious {
  background: linear-gradient(
    135deg,
    rgba(234, 179, 8, 0.45) 0%,
    rgba(202, 138, 4, 0.65) 25%,
    rgba(161, 98, 7, 0.85) 50%,
    rgba(245, 158, 11, 0.65) 75%,
    rgba(217, 119, 6, 0.45) 100%
  ) !important;
  border: 1px solid rgba(234, 179, 8, 0.6) !important;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(234, 179, 8, 0.3) !important;
}

/* Creative - Magenta/Pink Gradient */
.persona-overview-card.creative {
  background: linear-gradient(
    135deg,
    rgba(236, 72, 153, 0.45) 0%,
    rgba(219, 39, 119, 0.65) 25%,
    rgba(190, 24, 93, 0.85) 50%,
    rgba(225, 29, 72, 0.65) 75%,
    rgba(190, 18, 60, 0.45) 100%
  ) !important;
  border: 1px solid rgba(236, 72, 153, 0.6) !important;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(236, 72, 153, 0.3) !important;
}

/* Pseudo-element for animated border, only visible on hover */
.neumorphic-container::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.2s;
}

.neumorphic-container:hover::before {
  /* Show border effect on hover */
  opacity: 1;
  padding: 1px; /* matches border width */
  background: radial-gradient(circle, #dd7bbbff 10%, #dd7bbb00 20%),
    radial-gradient(circle at 40% 40%, #d79f1eff 5%, #d79f1e00 15%),
    radial-gradient(circle at 60% 60%, #5a922cff 10%, #5a922c00 20%),
    radial-gradient(circle at 40% 60%, #4c7894ff 10%, #4c789400 20%),
    repeating-conic-gradient(
      from 236.84deg at 50% 50%,
      #dd7bbb 0%,
      #d79f1e calc(25% / 5),
      #5a922c calc(50% / 5),
      #4c7894 calc(75% / 5),
      #dd7bbb calc(100% / 5)
    );
  /* Mask to show only the border area */
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

/* Neumorphic Elevated */
.neumorphic-elevated {
  background-color: var(--color-neumorphic-bg);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(15px) brightness(105%) saturate(115%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  /* box-shadow: var(--shadow-neumorphic-elevated); */
  transition: all var(--transition-base);
}

.neumorphic-elevated:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(18px) brightness(110%) saturate(120%);
  /* outline: 3px solid;
  outline-offset: 0px;
  outline-color: #ff6b6b;
  animation: borderGradient 3s ease infinite; */
}

/* Neumorphic Inset */
.neumorphic-inset {
  background-color: var(--color-neumorphic-bg);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(15px) brightness(105%) saturate(115%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: var(--shadow-neumorphic-container);
  transition: all var(--transition-base);
}

/* Neumorphic Header */
.neumorphic-header {
  background-color: var(--color-neumorphic-bg);
  border-bottom: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-neumorphic-elevated);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* ==========================================================================
   GLASS EFFECTS
   ========================================================================== */

.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* ==========================================================================
   TEXT EFFECTS
   ========================================================================== */

.text-shadow-neumorphic {
  text-shadow: 1px 1px 2px var(--color-neumorphic-shadow-dark),
    -1px -1px 2px var(--color-neumorphic-shadow-light);
}

.text-shadow-sm {
  text-shadow: 0 1px 2px rgb(0 0 0 / 0.1);
}

.text-shadow-md {
  text-shadow: 0 4px 8px rgb(0 0 0 / 0.12);
}

.text-shadow-lg {
  text-shadow: 0 8px 16px rgb(0 0 0 / 0.15);
}

/* ==========================================================================
   UTILITY CLASSES
   ========================================================================== */

/* Layout Utilities */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.page-container {
  padding: var(--spacing-2);
  margin-top: var(--spacing-16);
}

.page-header {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-6);
}

.page-subheader {
  color: var(--color-gray-500);
  margin-top: var(--spacing-2);
  font-size: var(--font-size-lg);
}

/* Flexbox Utilities */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* Spacing Utilities */
.section-spacing {
  margin-bottom: var(--spacing-16);
}

.section-spacing-lg {
  margin-bottom: var(--spacing-20);
}

/* Text Utilities */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Line Clamp Utilities for YouTube-style cards */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.text-balance {
  text-wrap: balance;
}

/* Text Color Utilities */
.text-primary {
  color: var(--color-text-primary);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.text-secondary {
  color: var(--color-text-secondary);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
}

.text-muted {
  color: var(--color-text-muted);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
}

.text-inverse {
  color: var(--color-text-inverse);
}

.text-accent {
  color: var(--color-text-accent);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* ==========================================================================
   ANIMATIONS
   ========================================================================== */

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes borderGradient {
  0% {
    outline-color: #ff6b6b;
  }
  14% {
    outline-color: #4ecdc4;
  }
  28% {
    outline-color: #45b7d1;
  }
  42% {
    outline-color: #96ceb4;
  }
  56% {
    outline-color: #feca57;
  }
  70% {
    outline-color: #ff9ff3;
  }
  84% {
    outline-color: #54a0ff;
  }
  100% {
    outline-color: #ff6b6b;
  }
}

/* Animation Classes */
.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slideUp {
  animation: slideUp 0.3s ease-out forwards;
}

.animate-scaleIn {
  animation: scaleIn 0.2s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.6, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* ==========================================================================
   RESPONSIVE UTILITIES
   ========================================================================== */

/* Grid Responsive */
.grid-responsive {
  display: grid;
  gap: var(--spacing-6);
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

@media (min-width: 768px) {
  .grid-responsive {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }
}

@media (min-width: 1024px) {
  .grid-responsive {
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  }
}

/* ==========================================================================
   ACCESSIBILITY
   ========================================================================== */

/* Focus Styles */
.focus-ring {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-ring:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ==========================================================================
   PRINT STYLES
   ========================================================================== */

@media print {
  .no-print {
    display: none !important;
  }

  .print-break {
    page-break-before: always;
  }

  body {
    background: white !important;
    color: black !important;
  }
}
