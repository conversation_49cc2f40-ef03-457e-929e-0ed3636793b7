# 🎨 Scoot Insights Design System Guide

## 📋 Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Design Tokens](#design-tokens)
4. [Component Classes](#component-classes)
5. [Best Practices](#best-practices)
6. [Where to Change Styles](#where-to-change-styles)
7. [Migration Guide](#migration-guide)
8. [Examples](#examples)

## 🎯 Overview

The Scoot Insights project now uses a comprehensive design system built on CSS custom properties (design tokens) and organized component classes. This system provides:

- **Consistency**: All styles use the same design tokens
- **Maintainability**: Changes to design tokens update everywhere automatically
- **Scalability**: Easy to add new components and variants
- **Developer Experience**: Clear naming conventions and organization

### Key Principles

- **Single Source of Truth**: All design tokens are defined in CSS custom properties
- **Component-First**: Styles are organized by component type
- **Consistency**: Standardized class names and patterns
- **Maintainability**: Clear separation of concerns and easy-to-find styles

## 🏗️ Architecture

### File Structure

```
src/styles/
├── index.css          # Design tokens, global styles, neumorphic styles
├── components.css     # Component-specific styles organized by type
└── neumorphic.css     # Modal positioning and carousel overrides

tailwind.config.js     # Tailwind config using design tokens
```

### Style Organization

1. **Design Tokens** (`src/styles/index.css`): CSS custom properties for colors, typography, spacing, etc.
2. **Component Classes** (`src/styles/components.css`): Reusable component styles using design tokens
3. **Global Styles** (`src/styles/index.css`): Base styles, utilities, animations
4. **Tailwind Integration** (`tailwind.config.js`): Tailwind utilities that use design tokens

## 🎨 Design Tokens

All design tokens are defined as CSS custom properties in `src/styles/index.css`:

### Colors

```css
:root {
  /* Primary Colors */
  --color-primary-50: #eff6ff;
  --color-primary-500: #3b82f6;
  --color-primary-900: #1e3a8a;

  /* Secondary Colors */
  --color-secondary-50: #faf5ff;
  --color-secondary-500: #a855f7;
  --color-secondary-900: #581c87;

  /* Gray Scale */
  --color-gray-50: #f9fafb;
  --color-gray-500: #6b7280;
  --color-gray-900: #111827;

  /* Semantic Colors */
  --color-success-500: #22c55e;
  --color-warning-500: #f59e0b;
  --color-error-500: #ef4444;

  /* Neumorphic Colors */
  --color-neumorphic-bg: #e6ebf0;
  --color-neumorphic-shadow-light: #ffffff;
  --color-neumorphic-shadow-dark: #c2c9d1;
}
```

### Typography

```css
:root {
  --font-family-sans: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-base: 1rem;
  --font-size-3xl: 1.875rem;
  --font-weight-medium: 500;
  --font-weight-bold: 700;
  --line-height-normal: 1.5;
  --line-height-tight: 1.25;
}
```

### Spacing

```css
:root {
  --spacing-1: 0.25rem;
  --spacing-4: 1rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-16: 4rem;
}
```

### Layout

```css
:root {
  --sidebar-width-expanded: 256px;
  --sidebar-width-collapsed: 64px;
  --header-height: 64px;
  --container-max-width: 1280px;
  --container-padding: var(--spacing-8);
}
```

### Shadows

```css
:root {
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-neumorphic: 6px 6px 12px var(--color-neumorphic-shadow-dark), -6px -6px
      12px var(--color-neumorphic-shadow-light);
}
```

## 🧩 Component Classes

### Button Components

```css
.btn              /* Base button styles */
/* Base button styles */
/* Base button styles */
/* Base button styles */
.btn-primary      /* Primary button variant */
.btn-secondary    /* Secondary button variant */
.btn-ghost        /* Ghost button variant */
.btn-icon         /* Icon-only button */
.btn-sm           /* Small button */
.btn-lg; /* Large button */
```

**Usage:**

```jsx
<button className="btn btn-primary">Primary Action</button>
<button className="btn btn-secondary btn-sm">Small Secondary</button>
<button className="btn btn-icon">
  <Icon className="h-5 w-5" />
</button>
```

### Card Components

```css
.card             /* Base card container */
/* Base card container */
/* Base card container */
/* Base card container */
.card-header      /* Card header section */
.card-body        /* Card body section */
.card-footer      /* Card footer section */
.card-interactive /* Interactive card with hover effects */
.card-glass; /* Glass effect card */
```

**Usage:**

```jsx
<div className='card'>
  <div className='card-header'>
    <h3>Card Title</h3>
  </div>
  <div className='card-body'>
    <p>Card content</p>
  </div>
  <div className='card-footer'>
    <button className='btn btn-primary'>Action</button>
  </div>
</div>
```

### Form Components

```css
.form-input       /* Text input fields */
/* Text input fields */
/* Text input fields */
/* Text input fields */
.form-select      /* Select dropdown */
.form-label       /* Form labels */
.form-error; /* Error messages */
```

**Usage:**

```jsx
<form>
  <label className='form-label'>Email</label>
  <input className='form-input' type='email' placeholder='Enter email' />
  <div className='form-error'>Invalid email format</div>
</form>
```

### Modal Components

```css
.modal-backdrop    /* Modal backdrop */
/* Modal backdrop */
/* Modal backdrop */
/* Modal backdrop */
.modal-container   /* Modal container */
.modal-content     /* Modal content wrapper */
.modal-header      /* Modal header */
.modal-body        /* Modal body */
.modal-footer      /* Modal footer */
.modal-close; /* Close button */
```

**Usage:**

```jsx
<div className='modal-backdrop' onClick={onClose}>
  <div className='modal-container' onClick={(e) => e.stopPropagation()}>
    <div className='modal-content'>
      <div className='modal-header'>
        <h2 className='text-xl font-bold text-gray-900'>{persona.name}</h2>
        <button className='modal-close' onClick={onClose}>
          ×
        </button>
      </div>

      <div className='modal-body'>
        <div className='space-y-4'>
          <div className='flex items-center space-x-4'>
            <img
              className='w-16 h-16 rounded-full object-cover'
              src={persona.avatar}
              alt={persona.name}
            />
            <div>
              <h3 className='text-lg font-semibold text-gray-900'>
                {persona.name}
              </h3>
              <span className='badge badge-primary'>{persona.category}</span>
            </div>
          </div>

          <p className='text-gray-600'>{persona.description}</p>
        </div>
      </div>

      <div className='modal-footer'>
        <button className='btn btn-secondary' onClick={onClose}>
          Cancel
        </button>
        <button className='btn btn-primary'>Save Changes</button>
      </div>
    </div>
  </div>
</div>
```

### Navigation Components

```css
.nav-item         /* Navigation item */
/* Navigation item */
/* Navigation item */
/* Navigation item */
.nav-item-active  /* Active navigation item */
.nav-item-inactive /* Inactive navigation item */
.nav-item-icon; /* Navigation item icon */
```

### Badge Components

```css
.badge            /* Base badge */
/* Base badge */
/* Base badge */
/* Base badge */
.badge-primary    /* Primary badge */
.badge-secondary  /* Secondary badge */
.badge-success    /* Success badge */
.badge-warning    /* Warning badge */
.badge-error; /* Error badge */
```

### Progress Components

```css
.progress-bar     /* Progress bar container */
/* Progress bar container */
/* Progress bar container */
/* Progress bar container */
.progress-fill    /* Progress bar fill */
.progress-label; /* Progress label */
```

### Timeline Components

```css
.timeline-container    /* Timeline container */
/* Timeline container */
/* Timeline container */
/* Timeline container */
.timeline-line        /* Timeline line */
.timeline-item        /* Timeline item */
.timeline-dot         /* Timeline dot */
.timeline-content     /* Timeline content */
.timeline-title       /* Timeline title */
.timeline-date; /* Timeline date */
```

### Persona Components

```css
.persona-card         /* Persona card */
/* Persona card */
/* Persona card */
/* Persona card */
.persona-card-header  /* Persona card header */
.persona-card-avatar  /* Persona avatar */
.persona-card-body    /* Persona card body */
.persona-card-footer  /* Persona card footer */
.persona-category-badge; /* Persona category badge */
```

### Carousel Components

```css
.carousel-arrow       /* Carousel navigation arrow */
/* Carousel navigation arrow */
/* Carousel navigation arrow */
/* Carousel navigation arrow */
.carousel-dot         /* Carousel pagination dot */
.carousel-dot-active  /* Active carousel dot */
.carousel-dot-inactive; /* Inactive carousel dot */
```

## 🎯 Best Practices

### 1. Use Design Tokens

**✅ Good:**

```jsx
<div className='bg-primary-500 text-white p-4 rounded-lg'>Content</div>
```

**❌ Bad:**

```jsx
<div className='bg-blue-600 text-white p-4 rounded-lg'>Content</div>
```

### 2. Use Component Classes

**✅ Good:**

```jsx
<button className='btn btn-primary'>Action</button>
```

**❌ Bad:**

```jsx
<button className='inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium bg-primary-600 text-white hover:bg-primary-700'>
  Action
</button>
```

### 3. Combine Component Classes with Tailwind Utilities

**✅ Good:**

```jsx
<div className='card mb-6'>
  <div className='card-header flex-between'>
    <h3>Title</h3>
    <button className='btn btn-icon'>×</button>
  </div>
</div>
```

### 4. Use Semantic Class Names

**✅ Good:**

```jsx
<div className='persona-card'>
  <img className='persona-card-avatar' src='...' alt='...' />
</div>
```

**❌ Bad:**

```jsx
<div className='rounded-xl shadow-lg overflow-hidden'>
  <img className='w-12 h-12 rounded-full object-cover' src='...' alt='...' />
</div>
```

### 5. Avoid Inline Styles

**✅ Good:**

```jsx
<div className='progress-fill' style={{ width: `${progressPercentage}%` }} />
```

**❌ Bad:**

```jsx
<div
  style={{
    width: "50%",
    backgroundColor: "#3b82f6",
    borderRadius: "9999px",
    height: "8px",
  }}
/>
```

## 🔧 Where to Change Styles

### Global Changes

- **Colors**: Update CSS custom properties in `src/styles/index.css`
- **Typography**: Update font variables in `src/styles/index.css`
- **Spacing**: Update spacing variables in `src/styles/index.css`
- **Shadows**: Update shadow variables in `src/styles/index.css`

### Component-Specific Changes

- **Button styles**: `src/styles/components.css` (`.btn-*` classes)
- **Card styles**: `src/styles/components.css` (`.card-*` classes)
- **Form styles**: `src/styles/components.css` (`.form-*` classes)
- **Modal styles**: `src/styles/components.css` (`.modal-*` classes)
- **Navigation styles**: `src/styles/components.css` (`.nav-*` classes)

### Neumorphic Styles

- **Neumorphic effects**: `src/styles/index.css` (`.neumorphic-*` classes)
- **Glass effects**: `src/styles/index.css` (`.glass-*` classes)

### Layout Styles

- **Page layout**: `src/styles/index.css` (`.page-*`, `.container` classes)
- **Grid layouts**: `src/styles/components.css` (`.grid-responsive`)

## 🚀 Migration Guide

### Step 1: Replace Hardcoded Values

**Before:**

```jsx
<div className='bg-blue-600 text-white p-4 rounded-lg'>Content</div>
```

**After:**

```jsx
<div className='bg-primary-600 text-white p-4 rounded-lg'>Content</div>
```

### Step 2: Use Component Classes

**Before:**

```jsx
<button className='inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium bg-blue-600 text-white hover:bg-blue-700'>
  Action
</button>
```

**After:**

```jsx
<button className='btn btn-primary'>Action</button>
```

### Step 3: Replace Long Tailwind Strings

**Before:**

```jsx
<div className='bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden'>
  <div className='p-6 border-b border-gray-100'>
    <h3>Title</h3>
  </div>
  <div className='p-6'>
    <p>Content</p>
  </div>
</div>
```

**After:**

```jsx
<div className='card'>
  <div className='card-header'>
    <h3>Title</h3>
  </div>
  <div className='card-body'>
    <p>Content</p>
  </div>
</div>
```

### Step 4: Update Inline Styles

**Before:**

```jsx
<div
  style={{
    backgroundColor: "#3b82f6",
    color: "white",
    padding: "16px",
    borderRadius: "8px",
  }}
>
  Content
</div>
```

**After:**

```jsx
<div className='bg-primary-600 text-white p-4 rounded-lg'>Content</div>
```

## 📝 Examples

### Complete Component Example

```jsx
import React from "react";

const PersonaCard = ({ persona }) => {
  return (
    <div className='persona-card'>
      <div className='persona-card-header'>
        <img
          className='persona-card-avatar'
          src={persona.avatar}
          alt={persona.name}
        />
        <div>
          <h3 className='text-lg font-semibold text-gray-900'>
            {persona.name}
          </h3>
          <span className='badge badge-primary'>{persona.category}</span>
        </div>
      </div>

      <div className='persona-card-body'>
        <p className='text-gray-600 mb-4'>{persona.description}</p>

        <div className='space-y-2'>
          {persona.traits.map((trait, index) => (
            <div key={index} className='flex items-center'>
              <div className='w-2 h-2 rounded-full bg-primary-500 mr-3' />
              <span className='text-sm text-gray-600'>{trait}</span>
            </div>
          ))}
        </div>
      </div>

      <div className='persona-card-footer'>
        <button className='btn btn-primary btn-sm'>View Details</button>
      </div>
    </div>
  );
};

export default PersonaCard;
```

### Modal Example

```jsx
import React from "react";

const PersonaModal = ({ persona, isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className='modal-backdrop' onClick={onClose}>
      <div className='modal-container' onClick={(e) => e.stopPropagation()}>
        <div className='modal-content'>
          <div className='modal-header'>
            <h2 className='text-xl font-bold text-gray-900'>{persona.name}</h2>
            <button className='modal-close' onClick={onClose}>
              ×
            </button>
          </div>

          <div className='modal-body'>
            <div className='space-y-4'>
              <div className='flex items-center space-x-4'>
                <img
                  className='w-16 h-16 rounded-full object-cover'
                  src={persona.avatar}
                  alt={persona.name}
                />
                <div>
                  <h3 className='text-lg font-semibold text-gray-900'>
                    {persona.name}
                  </h3>
                  <span className='badge badge-primary'>
                    {persona.category}
                  </span>
                </div>
              </div>

              <p className='text-gray-600'>{persona.description}</p>
            </div>
          </div>

          <div className='modal-footer'>
            <button className='btn btn-secondary' onClick={onClose}>
              Cancel
            </button>
            <button className='btn btn-primary'>Save Changes</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PersonaModal;
```

## 🔍 Troubleshooting

### Common Issues

1. **Styles not applying**: Check if the CSS file is imported in `src/main.tsx`
2. **Design tokens not working**: Ensure Tailwind config is using CSS custom properties
3. **Component classes missing**: Check if the class is defined in `src/styles/components.css`
4. **Conflicting styles**: Use browser dev tools to check CSS specificity

### Debugging Tips

1. **Inspect element**: Use browser dev tools to see which styles are applied
2. **Check CSS cascade**: Ensure styles are loaded in the correct order
3. **Verify class names**: Double-check class names for typos
4. **Test in isolation**: Create a minimal test case to isolate issues

---

**Last Updated**: December 2024  
**Version**: 2.0.0  
**Maintainer**: Development Team
