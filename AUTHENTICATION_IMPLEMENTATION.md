# Authentication Implementation

## Overview

The application now has PIN-based authentication for all destructive operations (create, update, delete) on brands and clients. Authentication does not expire until the admin logs out.

## 🔐 Authentication Flow

### 1. Admin Authentication

- Admin enters PIN on the admin page
- PIN is validated against the database
- If valid, PIN is stored in `sessionStorage` as `adminPin`
- Authentication status is stored as `adminAuthenticated: "true"`

### 2. API Requests

- All API requests automatically include the admin PIN in the `X-Admin-PIN` header
- Backend middleware validates the PIN for protected routes
- If PIN is invalid or missing, request is rejected with 401 status

### 3. Logout

- Admin clicks logout button
- `sessionStorage` is cleared (both `adminPin` and `adminAuthenticated`)
- Admin is redirected to home page

## 🛡️ Protected Routes

### Brand Routes (Require Admin PIN)

- `POST /api/brands` - Create new brand
- `PUT /api/brands/:id` - Update brand
- `DELETE /api/brands/:id` - Delete brand

### Client Routes (Require Admin PIN)

- `POST /api/clients` - Create new client
- `PUT /api/clients/:id` - Update client
- `DELETE /api/clients/:id` - Delete client (soft delete)

### Upload Routes (Require Admin PIN)

- `POST /api/upload/logo` - Upload logo
- `PUT /api/upload/logo/replace` - Replace logo
- `DELETE /api/upload/logo` - Delete logo

### Public Routes (No Authentication Required)

- `GET /api/brands/*` - Read brand data
- `GET /api/clients/*` - Read client data
- `GET /api/upload/status` - Check S3 status

## 🔧 Implementation Details

### Backend Middleware

**File**: `server/src/middleware/auth.ts`

```typescript
// Main authentication middleware
export const requireAdminAuth = async (req, res, next) => {
  const adminPin = req.headers["x-admin-pin"];
  // Validates PIN against database
  // Sets req.isAdminAuthenticated = true if valid
};

// Optional authentication check
export const checkAdminAuth = async (req, res, next) => {
  // Sets req.isAdminAuthenticated flag without blocking
};
```

### Frontend Integration

**File**: `src/services/apiClient.ts`

```typescript
// Request interceptor automatically adds PIN to headers
apiClient.interceptors.request.use((config) => {
  const adminPin = sessionStorage.getItem("adminPin");
  if (adminPin) {
    config.headers["X-Admin-PIN"] = adminPin;
  }
  return config;
});
```

### Authentication Storage

**File**: `src/utils/auth.ts`

```typescript
// Store authentication
sessionStorage.setItem("adminAuthenticated", "true");
sessionStorage.setItem("adminPin", pin);

// Clear authentication
sessionStorage.removeItem("adminAuthenticated");
sessionStorage.removeItem("adminPin");
```

## 🔒 Security Features

### PIN Validation

- **Case insensitive**: PINs are converted to uppercase for comparison
- **Database storage**: PINs are stored hashed in the database
- **6-character requirement**: PINs must be exactly 6 characters
- **Alphanumeric only**: PINs can only contain letters and numbers

### Error Handling

- **401 Unauthorized**: Invalid or missing PIN
- **500 Internal Server Error**: Database connection issues
- **Graceful degradation**: Frontend handles authentication errors

### Data Protection

- **No PIN exposure**: PIN is never logged or exposed in error messages
- **Secure headers**: PIN is transmitted via HTTP headers
- **Session-based**: PIN persists until logout

## 🧪 Testing Authentication

### Test Protected Route (Should Fail)

```bash
curl -X DELETE https://marketing-api.bloodandtreasure.com/brands/some-id
# Returns: 401 Unauthorized - "Admin PIN is required"
```

### Test Protected Route (Should Succeed)

```bash
curl -X DELETE \
  -H "X-Admin-PIN: ADMIN1" \
  https://marketing-api.bloodandtreasure.com/brands/some-id
# Returns: 200 OK or appropriate response
```

## 📝 Default Admin PIN

The default admin PIN is: **ADMIN1**

This is created during database seeding and can be changed through the admin interface.

## 🔄 Migration Notes

### What Changed

1. **Added authentication middleware** to protect destructive operations
2. **Updated API client** to automatically include PIN in headers
3. **Protected routes** now require valid admin PIN
4. **No changes to read operations** - they remain public

### Backward Compatibility

- **GET requests** continue to work without authentication
- **Existing frontend code** automatically includes PIN when authenticated
- **No breaking changes** to public API endpoints

## 🚀 Deployment

The authentication system is ready for production deployment. Ensure:

1. **Database is seeded** with admin PIN
2. **Environment variables** are properly configured
3. **HTTPS is enabled** for secure PIN transmission
4. **CORS is configured** to allow the frontend domain

## 🔍 Monitoring

### Logs to Monitor

- Authentication failures (401 responses)
- PIN validation errors
- Database connection issues during auth

### Metrics to Track

- Number of authentication attempts
- Failed authentication attempts
- Successful admin operations
